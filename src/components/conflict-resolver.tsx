"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetFooter } from '@/components/ui/sheet';
import { conflictResolutionSuggestions } from '@/ai/flows/conflict-resolution-suggestions';
import { ScheduleEntry, Lecturer, Classroom } from '@/lib/types';
import { Bo<PERSON>, Loader2, Sparkles } from 'lucide-react';
import { Card, CardContent } from './ui/card';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';

interface ConflictResolverProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  scheduleEntries: ScheduleEntry[];
  lecturers: Lecturer[];
  classrooms: Classroom[];
}

export default function ConflictResolver({
  isOpen,
  onOpenChange,
  scheduleEntries,
  lecturers,
  classrooms,
}: ConflictResolverProps) {
  const [requirements, setRequirements] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGetSuggestions = async () => {
    setIsLoading(true);
    setError(null);
    setSuggestions([]);

    const input = {
      scheduleEntries: scheduleEntries.map(e => ({...e, start_time: `${e.day_of_week} ${e.start_time}`, end_time: `${e.day_of_week} ${e.end_time}`})),
      lecturers,
      classrooms,
      requirements,
    };

    try {
      const result = await conflictResolutionSuggestions(input);
      setSuggestions(result.suggestions);
    } catch (err) {
      setError('Failed to get suggestions. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>AI Conflict Resolver</SheetTitle>
          <SheetDescription>
            Describe your scheduling preferences and constraints, and let AI suggest optimal solutions.
          </SheetDescription>
        </SheetHeader>
        <div className="py-4 space-y-4">
          <Textarea
            placeholder="e.g., 'Prioritize back-to-back classes for Year 2 students. Avoid scheduling practicals on Fridays. Dr. Smith prefers not to teach after 3 PM.'"
            value={requirements}
            onChange={(e) => setRequirements(e.target.value)}
            rows={5}
            className="font-code"
          />
        </div>

        {isLoading && (
            <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
        )}
        
        {error && (
             <Alert variant="destructive">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        )}

        {suggestions.length > 0 && (
            <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2"><Sparkles className="h-4 w-4 text-accent" /> AI Suggestions</h3>
                <Card>
                    <CardContent className="p-4 space-y-2">
                        <ul className="list-disc pl-5 space-y-2">
                        {suggestions.map((suggestion, index) => (
                            <li key={index} className="text-sm">{suggestion}</li>
                        ))}
                        </ul>
                    </CardContent>
                </Card>
            </div>
        )}

        <SheetFooter>
          <Button onClick={handleGetSuggestions} disabled={isLoading} className="w-full">
            {isLoading ? (
                <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                </>
            ) : (
                <>
                    <Bot className="mr-2 h-4 w-4" />
                    Get Suggestions
                </>
            )}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
