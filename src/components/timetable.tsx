"use client";

import { ScheduleEntry } from '@/lib/types';
import ScheduleEntryCard from './schedule-entry-card';
import { Card, CardContent } from './ui/card';

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const TIME_SLOTS = [
  '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'
];

interface TimetableProps {
  scheduleEntries: ScheduleEntry[];
  onSlotClick: (day: string, time: string) => void;
}

const Timetable = ({ scheduleEntries, onSlotClick }: TimetableProps) => {
  const getEntryForSlot = (day: string, time: string) => {
    return scheduleEntries.find(
      (entry) => entry.day_of_week === day && entry.start_time === time
    );
  };

  return (
    <Card>
      <CardContent className="p-0">
        <div className="grid grid-cols-6 border-t border-l rounded-lg overflow-hidden">
          {/* Header Row */}
          <div className="p-2 border-b border-r bg-card"></div>
          {DAYS.map((day) => (
            <div
              key={day}
              className="p-3 text-center font-semibold border-b border-r bg-card"
            >
              {day}
            </div>
          ))}

          {/* Time Slots Rows */}
          {TIME_SLOTS.slice(0, -1).map((time, index) => (
            <div key={time} className="grid grid-cols-6 contents">
              <div className="p-3 text-center font-semibold border-b border-r bg-card flex items-center justify-center">
                <span>
                  {time} - {TIME_SLOTS[index + 1]}
                </span>
              </div>
              {DAYS.map((day) => {
                const entry = getEntryForSlot(day, time);
                return (
                  <div
                    key={`${day}-${time}`}
                    className="p-1 border-b border-r min-h-[120px] bg-background/50 hover:bg-accent/50 transition-colors duration-200"
                    onClick={() => !entry && onSlotClick(day, time)}
                  >
                    {entry ? (
                      <ScheduleEntryCard entry={entry} />
                    ) : (
                      <div className="w-full h-full cursor-pointer"></div>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default Timetable;
