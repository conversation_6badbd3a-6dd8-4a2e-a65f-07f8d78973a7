"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Lecturer } from '@/lib/types';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AlertTriangle, Video } from 'lucide-react';
import { useEffect, useState } from 'react';
import { getModules, getLecturers } from '@/lib/api';

interface ScheduleEntryCardProps {
  entry: ScheduleEntry;
}

const ScheduleEntryCard = ({ entry }: ScheduleEntryCardProps) => {
  const [module, setModule] = useState<Module | null>(null);
  const [lecturer, setLecturer] = useState<Lecturer | null>(null);

  useEffect(() => {
    const fetchDetails = async () => {
      const modules = await getModules();
      const lecturers = await getLecturers();
      setModule(modules.find(m => m.id === entry.module_id) || null);
      setLecturer(lecturers.find(l => l.id === entry.lecturer_id) || null);
    };
    fetchDetails();
  }, [entry.module_id, entry.lecturer_id]);

  return (
    <Card
      className={cn(
        'h-full w-full shadow-md hover:shadow-lg transition-shadow duration-200 relative',
        entry.is_conflicted && 'border-destructive border-2'
      )}
    >
      <CardContent className="p-2 text-xs">
        <p className="font-bold truncate">{module?.module_name || 'Loading...'}</p>
        <p className="text-muted-foreground truncate">{lecturer?.name || '...'}</p>
        <p className="text-muted-foreground">CR: {entry.classroom_id}</p>
        <p className="text-muted-foreground">Group: {entry.group_id}</p>
        <div className="absolute bottom-1 right-1 flex gap-1">
          {entry.is_online && (
            <Badge variant="secondary" className="p-1">
              <Video className="h-3 w-3" />
            </Badge>
          )}
          {entry.is_conflicted && (
            <Badge variant="destructive" className="p-1">
              <AlertTriangle className="h-3 w-3" />
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ScheduleEntryCard;
