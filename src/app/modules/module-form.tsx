"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>alog, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Module } from "@/lib/types"
import { createModule, updateModule } from "@/lib/api-firestore"
import { useToast } from "@/hooks/use-toast"

interface ModuleFormProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  module?: Module | null
  onSuccess: () => void
}

export function ModuleForm({ isOpen, onOpenChange, module, onSuccess }: ModuleFormProps) {
  const [formData, setFormData] = useState({
    module_name: '',
    required_hours: 0,
    practical_or_theory: 'Theory' as 'Practical' | 'Theory',
  })
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (module) {
      setFormData({
        module_name: module.module_name,
        required_hours: module.required_hours,
        practical_or_theory: module.practical_or_theory,
      })
    } else {
      setFormData({
        module_name: '',
        required_hours: 0,
        practical_or_theory: 'Theory',
      })
    }
  }, [module])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (module) {
        await updateModule(module.id, formData)
        toast({
          title: "Success",
          description: "Module updated successfully",
        })
      } else {
        await createModule(formData)
        toast({
          title: "Success",
          description: "Module created successfully",
        })
      }

      onSuccess()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save module",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{module ? 'Edit Module' : 'Add New Module'}</DialogTitle>
          <DialogDescription>
            {module ? 'Update module information.' : 'Add a new module to the system.'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="module_name">Module Name</Label>
              <Input
                id="module_name"
                value={formData.module_name}
                onChange={(e) => setFormData(prev => ({ ...prev, module_name: e.target.value }))}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="required_hours">Required Hours</Label>
              <Input
                id="required_hours"
                type="number"
                min="1"
                value={formData.required_hours}
                onChange={(e) => setFormData(prev => ({ ...prev, required_hours: parseInt(e.target.value) || 0 }))}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="practical_or_theory">Type</Label>
              <Select
                value={formData.practical_or_theory}
                onValueChange={(value: 'Practical' | 'Theory') => 
                  setFormData(prev => ({ ...prev, practical_or_theory: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select module type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Theory">Theory</SelectItem>
                  <SelectItem value="Practical">Practical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : module ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
