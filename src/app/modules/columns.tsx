"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Modu<PERSON> } from "@/lib/types"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Edit, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface ColumnsProps {
  onEdit: (module: Module) => void;
  onDelete: (module: Module) => void;
}

export const createColumns = ({ onEdit, onDelete }: ColumnsProps): ColumnDef<Module>[] => [
  {
    accessorKey: "module_name",
    header: "Module Name",
  },
  {
    accessorKey: "required_hours",
    header: "Required Hours",
    cell: ({ row }) => {
      return <span>{row.getValue("required_hours")} hours</span>
    },
  },
  {
    accessorKey: "practical_or_theory",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("practical_or_theory") as string
      return (
        <Badge variant={type === "Practical" ? "default" : "secondary"}>
          {type}
        </Badge>
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const module = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(module.id)}
            >
              Copy Module ID
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEdit(module)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="text-destructive"
              onClick={() => onDelete(module)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
