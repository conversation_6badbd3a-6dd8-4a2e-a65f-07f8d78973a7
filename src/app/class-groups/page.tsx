"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ClassGroup } from "@/lib/types";
import { getClassGroups, deleteClassGroup } from "@/lib/api-firestore";
import { createColumns } from "./columns";
import { DataTable } from "./data-table";
import { ClassGroupForm } from "./class-group-form";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";

export default function ClassGroupsPage() {
  const [data, setData] = useState<ClassGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingClassGroup, setEditingClassGroup] = useState<ClassGroup | null>(null);
  const [deletingClassGroup, setDeletingClassGroup] = useState<ClassGroup | null>(null);
  const { toast } = useToast();

  const fetchData = async () => {
    setLoading(true);
    try {
      const classGroups = await getClassGroups();
      setData(classGroups);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch class groups",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleEdit = (classGroup: ClassGroup) => {
    setEditingClassGroup(classGroup);
    setIsFormOpen(true);
  };

  const handleDelete = (classGroup: ClassGroup) => {
    setDeletingClassGroup(classGroup);
  };

  const confirmDelete = async () => {
    if (!deletingClassGroup) return;

    try {
      await deleteClassGroup(deletingClassGroup.id);
      toast({
        title: "Success",
        description: "Class group deleted successfully",
      });
      fetchData();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete class group",
        variant: "destructive",
      });
    } finally {
      setDeletingClassGroup(null);
    }
  };

  const handleFormSuccess = () => {
    fetchData();
    setEditingClassGroup(null);
  };

  const handleFormClose = (open: boolean) => {
    setIsFormOpen(open);
    if (!open) {
      setEditingClassGroup(null);
    }
  };

  const columns = createColumns({ onEdit: handleEdit, onDelete: handleDelete });

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-headline font-bold">Manage Class Groups</h1>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Class Group
        </Button>
      </div>

      {loading ? (
        <p>Loading...</p>
      ) : (
        <DataTable
          columns={columns}
          data={data}
          filterColumn="program_name"
          filterPlaceholder="Filter by program name..."
        />
      )}

      <ClassGroupForm
        isOpen={isFormOpen}
        onOpenChange={handleFormClose}
        classGroup={editingClassGroup}
        onSuccess={handleFormSuccess}
      />

      <AlertDialog open={!!deletingClassGroup} onOpenChange={() => setDeletingClassGroup(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the class group
              "{deletingClassGroup?.program_name} - Year {deletingClassGroup?.year_level}" from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
