"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ClassGroup } from "@/lib/types"
import { createClassGroup, updateClassGroup } from "@/lib/api-firestore"
import { useToast } from "@/hooks/use-toast"

interface ClassGroupFormProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  classGroup?: ClassGroup | null
  onSuccess: () => void
}

export function ClassGroupForm({ isOpen, onOpenChange, classGroup, onSuccess }: ClassGroupFormProps) {
  const [formData, setFormData] = useState({
    program_name: '',
    year_level: 1,
    group_size: 0,
  })
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (classGroup) {
      setFormData({
        program_name: classGroup.program_name,
        year_level: classGroup.year_level,
        group_size: classGroup.group_size,
      })
    } else {
      setFormData({
        program_name: '',
        year_level: 1,
        group_size: 0,
      })
    }
  }, [classGroup])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (classGroup) {
        await updateClassGroup(classGroup.id, formData)
        toast({
          title: "Success",
          description: "Class group updated successfully",
        })
      } else {
        await createClassGroup(formData)
        toast({
          title: "Success",
          description: "Class group created successfully",
        })
      }

      onSuccess()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save class group",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{classGroup ? 'Edit Class Group' : 'Add New Class Group'}</DialogTitle>
          <DialogDescription>
            {classGroup ? 'Update class group information.' : 'Add a new class group to the system.'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="program_name">Program Name</Label>
              <Input
                id="program_name"
                value={formData.program_name}
                onChange={(e) => setFormData(prev => ({ ...prev, program_name: e.target.value }))}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="year_level">Year Level</Label>
              <Select
                value={formData.year_level.toString()}
                onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, year_level: parseInt(value) }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select year level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Year 1</SelectItem>
                  <SelectItem value="2">Year 2</SelectItem>
                  <SelectItem value="3">Year 3</SelectItem>
                  <SelectItem value="4">Year 4</SelectItem>
                  <SelectItem value="5">Year 5</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="group_size">Group Size</Label>
              <Input
                id="group_size"
                type="number"
                min="1"
                max="200"
                value={formData.group_size}
                onChange={(e) => setFormData(prev => ({ ...prev, group_size: parseInt(e.target.value) || 0 }))}
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : classGroup ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
