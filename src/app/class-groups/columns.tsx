"use client"

import { ColumnDef } from "@tanstack/react-table"
import { ClassGroup } from "@/lib/types"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Edit, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface ColumnsProps {
  onEdit: (classGroup: ClassGroup) => void;
  onDelete: (classGroup: ClassGroup) => void;
}

export const createColumns = ({ onEdit, onDelete }: ColumnsProps): ColumnDef<ClassGroup>[] => [
  {
    accessorKey: "program_name",
    header: "Program Name",
  },
  {
    accessorKey: "year_level",
    header: "Year Level",
    cell: ({ row }) => {
      const yearLevel = row.getValue("year_level") as number
      return (
        <Badge variant="outline">
          Year {yearLevel}
        </Badge>
      )
    },
  },
  {
    accessorKey: "group_size",
    header: "Group Size",
    cell: ({ row }) => {
      return <span>{row.getValue("group_size")} students</span>
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const classGroup = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(classGroup.id)}
            >
              Copy Class Group ID
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEdit(classGroup)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="text-destructive"
              onClick={() => onDelete(classGroup)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
