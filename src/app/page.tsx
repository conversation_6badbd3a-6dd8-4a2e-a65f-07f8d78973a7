"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import Timetable from '@/components/timetable';
import ConflictResolver from '@/components/conflict-resolver';
import { ScheduleEntry, Classroom, Lecturer, ClassGroup, Module } from '@/lib/types';
import { getScheduleEntries, getClassrooms, getLecturers, getClassGroups, getModules, seedInitialData } from '@/lib/api-firestore';
import { PlusCircle } from 'lucide-react';
import NewScheduleEntryDialog from '@/components/new-schedule-entry-dialog';

export default function DashboardPage() {
  const [scheduleEntries, setScheduleEntries] = useState<ScheduleEntry[]>([]);
  const [isConflictResolverOpen, setConflictResolverOpen] = useState(false);
  
  const [classrooms, setClassrooms] = useState<Classroom[]>([]);
  const [lecturers, setLecturers] = useState<Lecturer[]>([]);
  const [classGroups, setClassGroups] = useState<ClassGroup[]>([]);
  const [modules, setModules] = useState<Module[]>([]);

  const [isNewEntryDialogOpen, setIsNewEntryDialogOpen] = useState(false);
  const [newEntrySlot, setNewEntrySlot] = useState<{ day: string; time: string } | null>(null);
  
  const fetchData = async () => {
    try {
      const [
        scheduleData,
        classroomData,
        lecturerData,
        groupData,
        moduleData,
      ] = await Promise.all([
        getScheduleEntries(),
        getClassrooms(),
        getLecturers(),
        getClassGroups(),
        getModules(),
      ]);
      setScheduleEntries(scheduleData);
      setClassrooms(classroomData);
      setLecturers(lecturerData);
      setClassGroups(groupData);
      setModules(moduleData);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const handleSeedData = async () => {
    try {
      await seedInitialData();
      await fetchData();
    } catch (error) {
      console.error('Error seeding data:', error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleAddEntry = (entry: Omit<ScheduleEntry, 'id'>) => {
    const newEntry: ScheduleEntry = {
      ...entry,
      id: `se-${Date.now()}`,
    };
    setScheduleEntries(prev => [...prev, newEntry]);
    fetchData(); // Refetch to check for conflicts
  };

  const handleSlotClick = (day: string, time: string) => {
    setNewEntrySlot({ day, time });
    setIsNewEntryDialogOpen(true);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-headline font-bold">Weekly Timetable</h1>
        <div className="flex items-center gap-4">
          <Button onClick={() => setIsNewEntryDialogOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" /> Add Entry
          </Button>
          <Button onClick={() => setConflictResolverOpen(true)} variant="outline">
            Resolve Conflicts (AI)
          </Button>
          {process.env.NODE_ENV === 'development' && (
            <Button onClick={handleSeedData} variant="secondary">
              Seed Data
            </Button>
          )}
        </div>
      </div>
      
      <Timetable
        scheduleEntries={scheduleEntries}
        onSlotClick={handleSlotClick}
      />

      <ConflictResolver
        isOpen={isConflictResolverOpen}
        onOpenChange={setConflictResolverOpen}
        scheduleEntries={scheduleEntries}
        lecturers={lecturers}
        classrooms={classrooms}
      />
      
      <NewScheduleEntryDialog
        isOpen={isNewEntryDialogOpen}
        onOpenChange={setIsNewEntryDialogOpen}
        onAddEntry={handleAddEntry}
        classrooms={classrooms}
        lecturers={lecturers}
        classGroups={classGroups}
        modules={modules}
        initialSlot={newEntrySlot}
      />
    </div>
  );
}
