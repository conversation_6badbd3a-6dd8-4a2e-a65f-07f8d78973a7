"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Classroom } from "@/lib/types"
import { createClassroom, updateClassroom } from "@/lib/api-firestore"
import { useToast } from "@/hooks/use-toast"

interface ClassroomFormProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  classroom?: Classroom | null
  onSuccess: () => void
}

export function ClassroomForm({ isOpen, onOpenChange, classroom, onSuccess }: ClassroomFormProps) {
  const [formData, setFormData] = useState({
    room_number: '',
    capacity: 0,
    equipment_details: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (classroom) {
      setFormData({
        room_number: classroom.room_number,
        capacity: classroom.capacity,
        equipment_details: classroom.equipment_details,
      })
    } else {
      setFormData({
        room_number: '',
        capacity: 0,
        equipment_details: '',
      })
    }
  }, [classroom])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (classroom) {
        await updateClassroom(classroom.id, formData)
        toast({
          title: "Success",
          description: "Classroom updated successfully",
        })
      } else {
        await createClassroom(formData)
        toast({
          title: "Success",
          description: "Classroom created successfully",
        })
      }

      onSuccess()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save classroom",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{classroom ? 'Edit Classroom' : 'Add New Classroom'}</DialogTitle>
          <DialogDescription>
            {classroom ? 'Update classroom information.' : 'Add a new classroom to the system.'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="room_number">Room Number</Label>
              <Input
                id="room_number"
                value={formData.room_number}
                onChange={(e) => setFormData(prev => ({ ...prev, room_number: e.target.value }))}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="capacity">Capacity</Label>
              <Input
                id="capacity"
                type="number"
                min="1"
                max="500"
                value={formData.capacity}
                onChange={(e) => setFormData(prev => ({ ...prev, capacity: parseInt(e.target.value) || 0 }))}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="equipment_details">Equipment Details</Label>
              <Textarea
                id="equipment_details"
                value={formData.equipment_details}
                onChange={(e) => setFormData(prev => ({ ...prev, equipment_details: e.target.value }))}
                placeholder="e.g., Projector, Whiteboard, Computers..."
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : classroom ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
