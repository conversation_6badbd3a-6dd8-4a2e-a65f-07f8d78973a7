"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Classroom } from "@/lib/types"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Edit, Trash2 } from "lucide-react"

interface ColumnsProps {
  onEdit: (classroom: Classroom) => void;
  onDelete: (classroom: Classroom) => void;
}

export const createColumns = ({ onEdit, onDelete }: ColumnsProps): ColumnDef<Classroom>[] => [
  {
    accessorKey: "room_number",
    header: "Room Number",
  },
  {
    accessorKey: "capacity",
    header: "Capacity",
  },
  {
    accessorKey: "equipment_details",
    header: "Equipment",
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const classroom = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(classroom.id)}
            >
              Copy Classroom ID
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEdit(classroom)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive"
              onClick={() => onDelete(classroom)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
