"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Classroom } from "@/lib/types";
import { getClassrooms, deleteClassroom } from "@/lib/api-firestore";
import { createColumns } from "./columns";
import { DataTable } from "./data-table";
import { ClassroomForm } from "./classroom-form";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";

export default function ClassroomsPage() {
  const [data, setData] = useState<Classroom[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingClassroom, setEditingClassroom] = useState<Classroom | null>(null);
  const [deletingClassroom, setDeletingClassroom] = useState<Classroom | null>(null);
  const { toast } = useToast();

  const fetchData = async () => {
    setLoading(true);
    try {
      const classrooms = await getClassrooms();
      setData(classrooms);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch classrooms",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleEdit = (classroom: Classroom) => {
    setEditingClassroom(classroom);
    setIsFormOpen(true);
  };

  const handleDelete = (classroom: Classroom) => {
    setDeletingClassroom(classroom);
  };

  const confirmDelete = async () => {
    if (!deletingClassroom) return;

    try {
      await deleteClassroom(deletingClassroom.id);
      toast({
        title: "Success",
        description: "Classroom deleted successfully",
      });
      fetchData();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete classroom",
        variant: "destructive",
      });
    } finally {
      setDeletingClassroom(null);
    }
  };

  const handleFormSuccess = () => {
    fetchData();
    setEditingClassroom(null);
  };

  const handleFormClose = (open: boolean) => {
    setIsFormOpen(open);
    if (!open) {
      setEditingClassroom(null);
    }
  };

  const columns = createColumns({ onEdit: handleEdit, onDelete: handleDelete });

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-headline font-bold">Manage Classrooms</h1>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Classroom
        </Button>
      </div>

      {loading ? (
        <p>Loading...</p>
      ) : (
        <DataTable
          columns={columns}
          data={data}
          filterColumn="room_number"
          filterPlaceholder="Filter by room number..."
        />
      )}

      <ClassroomForm
        isOpen={isFormOpen}
        onOpenChange={handleFormClose}
        classroom={editingClassroom}
        onSuccess={handleFormSuccess}
      />

      <AlertDialog open={!!deletingClassroom} onOpenChange={() => setDeletingClassroom(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the classroom
              "{deletingClassroom?.room_number}" from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
