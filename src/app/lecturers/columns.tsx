"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Lecturer } from "@/lib/types"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Edit, Trash2 } from "lucide-react"

interface ColumnsProps {
  onEdit: (lecturer: Lecturer) => void;
  onDelete: (lecturer: Lecturer) => void;
}

export const createColumns = ({ onEdit, onDelete }: ColumnsProps): ColumnDef<Lecturer>[] => [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "contact_info",
    header: "Contact Info",
  },
  {
    accessorKey: "availability_preferences",
    header: "Availability",
    cell: ({ row }) => {
      const lecturer = row.original;

      // Handle both new and old data structures
      let preferences = { preferred_days: [], preferred_times: [] };

      if (lecturer.availability_preferences) {
        // New structure
        preferences = lecturer.availability_preferences;
      } else if (lecturer.availability_preferences_json) {
        // Old structure - parse J<PERSON><PERSON>
        try {
          preferences = JSON.parse(lecturer.availability_preferences_json);
        } catch {
          preferences = { preferred_days: [], preferred_times: [] };
        }
      }

      return (
        <div className="text-sm">
          <div>Days: {preferences.preferred_days?.join(", ") || "Not specified"}</div>
          <div>Times: {preferences.preferred_times?.join(", ") || "Not specified"}</div>
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const lecturer = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(lecturer.id)}
            >
              Copy Lecturer ID
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEdit(lecturer)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="text-destructive"
              onClick={() => onDelete(lecturer)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
