"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Lecturer } from "@/lib/types"
import { createLecturer, updateLecturer } from "@/lib/api-firestore"
import { useToast } from "@/hooks/use-toast"

interface LecturerFormProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  lecturer?: Lecturer | null
  onSuccess: () => void
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
const TIMES = ['morning', 'afternoon', 'evening']

export function LecturerForm({ isO<PERSON>, on<PERSON><PERSON><PERSON><PERSON><PERSON>, lecturer, onSuccess }: Lecturer<PERSON>ormP<PERSON>) {
  const [formData, setFormData] = useState({
    name: '',
    contact_info: '',
    preferred_days: [] as string[],
    preferred_times: [] as string[],
  })
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (lecturer) {
      // Handle both new and old data structures for backward compatibility
      let preferences = { preferred_days: [], preferred_times: [] };

      if (lecturer.availability_preferences) {
        // New structure
        preferences = lecturer.availability_preferences;
      } else if (lecturer.availability_preferences_json) {
        // Old structure - parse JSON
        try {
          preferences = JSON.parse(lecturer.availability_preferences_json);
        } catch {
          preferences = { preferred_days: [], preferred_times: [] };
        }
      }

      setFormData({
        name: lecturer.name,
        contact_info: lecturer.contact_info,
        preferred_days: preferences.preferred_days || [],
        preferred_times: preferences.preferred_times || [],
      })
    } else {
      setFormData({
        name: '',
        contact_info: '',
        preferred_days: [],
        preferred_times: [],
      })
    }
  }, [lecturer])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const lecturerData = {
        name: formData.name,
        contact_info: formData.contact_info,
        availability_preferences: {
          preferred_days: formData.preferred_days,
          preferred_times: formData.preferred_times,
        },
      }

      if (lecturer) {
        await updateLecturer(lecturer.id, lecturerData)
        toast({
          title: "Success",
          description: "Lecturer updated successfully",
        })
      } else {
        await createLecturer(lecturerData)
        toast({
          title: "Success",
          description: "Lecturer created successfully",
        })
      }

      onSuccess()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save lecturer",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDayChange = (day: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      preferred_days: checked 
        ? [...prev.preferred_days, day]
        : prev.preferred_days.filter(d => d !== day)
    }))
  }

  const handleTimeChange = (time: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      preferred_times: checked 
        ? [...prev.preferred_times, time]
        : prev.preferred_times.filter(t => t !== time)
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{lecturer ? 'Edit Lecturer' : 'Add New Lecturer'}</DialogTitle>
          <DialogDescription>
            {lecturer ? 'Update lecturer information.' : 'Add a new lecturer to the system.'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="contact_info">Contact Info</Label>
              <Input
                id="contact_info"
                type="email"
                value={formData.contact_info}
                onChange={(e) => setFormData(prev => ({ ...prev, contact_info: e.target.value }))}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label>Preferred Days</Label>
              <div className="flex flex-wrap gap-2">
                {DAYS.map(day => (
                  <div key={day} className="flex items-center space-x-2">
                    <Checkbox
                      id={`day-${day}`}
                      checked={formData.preferred_days.includes(day)}
                      onCheckedChange={(checked) => handleDayChange(day, checked as boolean)}
                    />
                    <Label htmlFor={`day-${day}`} className="text-sm">{day}</Label>
                  </div>
                ))}
              </div>
            </div>
            <div className="grid gap-2">
              <Label>Preferred Times</Label>
              <div className="flex flex-wrap gap-2">
                {TIMES.map(time => (
                  <div key={time} className="flex items-center space-x-2">
                    <Checkbox
                      id={`time-${time}`}
                      checked={formData.preferred_times.includes(time)}
                      onCheckedChange={(checked) => handleTimeChange(time, checked as boolean)}
                    />
                    <Label htmlFor={`time-${time}`} className="text-sm capitalize">{time}</Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : lecturer ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
