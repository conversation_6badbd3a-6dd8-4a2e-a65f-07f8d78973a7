"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Lecturer } from "@/lib/types";
import { getLecturers, deleteLecturer } from "@/lib/api-firestore";
import { createColumns } from "./columns";
import { DataTable } from "./data-table";
import { LecturerForm } from "./lecturer-form";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";

export default function LecturersPage() {
  const [data, setData] = useState<Lecturer[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingLecturer, setEditingLecturer] = useState<Lecturer | null>(null);
  const [deletingLecturer, setDeletingLecturer] = useState<Lecturer | null>(null);
  const { toast } = useToast();

  const fetchData = async () => {
    setLoading(true);
    try {
      const lecturers = await getLecturers();
      setData(lecturers);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch lecturers",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleEdit = (lecturer: Lecturer) => {
    setEditingLecturer(lecturer);
    setIsFormOpen(true);
  };

  const handleDelete = (lecturer: Lecturer) => {
    setDeletingLecturer(lecturer);
  };

  const confirmDelete = async () => {
    if (!deletingLecturer) return;

    try {
      await deleteLecturer(deletingLecturer.id);
      toast({
        title: "Success",
        description: "Lecturer deleted successfully",
      });
      fetchData();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete lecturer",
        variant: "destructive",
      });
    } finally {
      setDeletingLecturer(null);
    }
  };

  const handleFormSuccess = () => {
    fetchData();
    setEditingLecturer(null);
  };

  const handleFormClose = (open: boolean) => {
    setIsFormOpen(open);
    if (!open) {
      setEditingLecturer(null);
    }
  };

  const columns = createColumns({ onEdit: handleEdit, onDelete: handleDelete });

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-headline font-bold">Manage Lecturers</h1>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Lecturer
        </Button>
      </div>

      {loading ? (
        <p>Loading...</p>
      ) : (
        <DataTable
          columns={columns}
          data={data}
          filterColumn="name"
          filterPlaceholder="Filter by name..."
        />
      )}

      <LecturerForm
        isOpen={isFormOpen}
        onOpenChange={handleFormClose}
        lecturer={editingLecturer}
        onSuccess={handleFormSuccess}
      />

      <AlertDialog open={!!deletingLecturer} onOpenChange={() => setDeletingLecturer(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the lecturer
              "{deletingLecturer?.name}" from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
