import {
  classroomService,
  lecturerService,
  lecturerServiceExtended,
  classGroupService,
  moduleService,
  scheduleEntryService,
  scheduleEntryServiceExtended,
} from './firestore';
import type {
  Classroom,
  Lecturer,
  ClassGroup,
  Module,
  ScheduleEntry,
  ClassroomFormData,
  LecturerFormData,
  ClassGroupFormData,
  ModuleFormData,
  ScheduleEntryFormData,
} from './types';

// Classroom API
export const getClassrooms = async (): Promise<Classroom[]> => {
  return classroomService.getAll();
};

export const getClassroom = async (id: string): Promise<Classroom | null> => {
  return classroomService.getById(id);
};

export const createClassroom = async (data: ClassroomFormData): Promise<Classroom> => {
  return classroomService.create(data);
};

export const updateClassroom = async (id: string, data: Partial<ClassroomFormData>): Promise<Classroom> => {
  return classroomService.update(id, data);
};

export const deleteClassroom = async (id: string): Promise<void> => {
  return classroomService.delete(id);
};

// Lecturer API
export const getLecturers = async (): Promise<Lecturer[]> => {
  return lecturerService.getAll();
};

export const getLecturer = async (id: string): Promise<Lecturer | null> => {
  return lecturerService.getById(id);
};

export const createLecturer = async (data: LecturerFormData): Promise<Lecturer> => {
  return lecturerService.create(data);
};

export const updateLecturer = async (id: string, data: Partial<LecturerFormData>): Promise<Lecturer> => {
  return lecturerService.update(id, data);
};

export const deleteLecturer = async (id: string): Promise<void> => {
  return lecturerService.delete(id);
};

export const getLecturersByAvailability = async (day: string, time: string): Promise<Lecturer[]> => {
  return lecturerServiceExtended.getByAvailability(day, time);
};

// Class Group API
export const getClassGroups = async (): Promise<ClassGroup[]> => {
  return classGroupService.getAll();
};

export const getClassGroup = async (id: string): Promise<ClassGroup | null> => {
  return classGroupService.getById(id);
};

export const createClassGroup = async (data: ClassGroupFormData): Promise<ClassGroup> => {
  return classGroupService.create(data);
};

export const updateClassGroup = async (id: string, data: Partial<ClassGroupFormData>): Promise<ClassGroup> => {
  return classGroupService.update(id, data);
};

export const deleteClassGroup = async (id: string): Promise<void> => {
  return classGroupService.delete(id);
};

// Module API
export const getModules = async (): Promise<Module[]> => {
  return moduleService.getAll();
};

export const getModule = async (id: string): Promise<Module | null> => {
  return moduleService.getById(id);
};

export const createModule = async (data: ModuleFormData): Promise<Module> => {
  return moduleService.create(data);
};

export const updateModule = async (id: string, data: Partial<ModuleFormData>): Promise<Module> => {
  return moduleService.update(id, data);
};

export const deleteModule = async (id: string): Promise<void> => {
  return moduleService.delete(id);
};

// Schedule Entry API
export const getScheduleEntries = async (): Promise<ScheduleEntry[]> => {
  const entries = await scheduleEntryService.getAll();
  
  // Check for conflicts and update entries
  const entriesWithConflicts = await Promise.all(
    entries.map(async (entry) => {
      const conflicts = await scheduleEntryServiceExtended.checkConflicts({
        module_id: entry.module_id,
        lecturer_id: entry.lecturer_id,
        classroom_id: entry.classroom_id,
        group_id: entry.group_id,
        start_time: entry.start_time,
        end_time: entry.end_time,
        day_of_week: entry.day_of_week,
        is_online: entry.is_online,
      });
      
      // Filter out the current entry from conflicts
      const actualConflicts = conflicts.filter(conflict => conflict.id !== entry.id);
      
      return {
        ...entry,
        is_conflicted: actualConflicts.length > 0,
      };
    })
  );
  
  return entriesWithConflicts;
};

export const getScheduleEntry = async (id: string): Promise<ScheduleEntry | null> => {
  return scheduleEntryService.getById(id);
};

export const createScheduleEntry = async (data: ScheduleEntryFormData): Promise<ScheduleEntry> => {
  // Check for conflicts before creating
  const conflicts = await scheduleEntryServiceExtended.checkConflicts(data);
  
  const newEntry = await scheduleEntryService.create({
    ...data,
    is_conflicted: conflicts.length > 0,
  } as ScheduleEntryFormData);
  
  return newEntry;
};

export const updateScheduleEntry = async (id: string, data: Partial<ScheduleEntryFormData>): Promise<ScheduleEntry> => {
  // Get current entry to merge data for conflict checking
  const currentEntry = await scheduleEntryService.getById(id);
  if (!currentEntry) {
    throw new Error('Schedule entry not found');
  }
  
  const mergedData = {
    module_id: data.module_id ?? currentEntry.module_id,
    lecturer_id: data.lecturer_id ?? currentEntry.lecturer_id,
    classroom_id: data.classroom_id ?? currentEntry.classroom_id,
    group_id: data.group_id ?? currentEntry.group_id,
    start_time: data.start_time ?? currentEntry.start_time,
    end_time: data.end_time ?? currentEntry.end_time,
    day_of_week: data.day_of_week ?? currentEntry.day_of_week,
    is_online: data.is_online ?? currentEntry.is_online,
  };
  
  // Check for conflicts
  const conflicts = await scheduleEntryServiceExtended.checkConflicts(mergedData);
  const actualConflicts = conflicts.filter(conflict => conflict.id !== id);
  
  return scheduleEntryService.update(id, {
    ...data,
    is_conflicted: actualConflicts.length > 0,
  } as Partial<ScheduleEntryFormData>);
};

export const deleteScheduleEntry = async (id: string): Promise<void> => {
  return scheduleEntryService.delete(id);
};

// Schedule Entry Query Methods
export const getScheduleEntriesByDay = async (day: string): Promise<ScheduleEntry[]> => {
  return scheduleEntryServiceExtended.getByDay(day);
};

export const getScheduleEntriesByLecturer = async (lecturerId: string): Promise<ScheduleEntry[]> => {
  return scheduleEntryServiceExtended.getByLecturer(lecturerId);
};

export const getScheduleEntriesByClassroom = async (classroomId: string): Promise<ScheduleEntry[]> => {
  return scheduleEntryServiceExtended.getByClassroom(classroomId);
};

export const getScheduleEntriesByGroup = async (groupId: string): Promise<ScheduleEntry[]> => {
  return scheduleEntryServiceExtended.getByGroup(groupId);
};

export const checkScheduleConflicts = async (entry: ScheduleEntryFormData): Promise<ScheduleEntry[]> => {
  return scheduleEntryServiceExtended.checkConflicts(entry);
};

// Utility function to seed initial data (for development)
export const seedInitialData = async (): Promise<void> => {
  try {
    // Check if data already exists
    const existingClassrooms = await getClassrooms();
    if (existingClassrooms.length > 0) {
      console.log('Data already exists, skipping seed');
      return;
    }

    // Seed classrooms
    await createClassroom({
      room_number: 'A101',
      capacity: 50,
      equipment_details: 'Projector, Whiteboard',
    });
    
    await createClassroom({
      room_number: 'B203',
      capacity: 30,
      equipment_details: 'Computers, Projector',
    });

    // Seed lecturers
    await createLecturer({
      name: 'Dr. Alan Smith',
      contact_info: '<EMAIL>',
      availability_preferences: {
        preferred_days: ['Monday', 'Wednesday'],
        preferred_times: ['morning'],
      },
    });
    
    await createLecturer({
      name: 'Prof. Mary Jane',
      contact_info: '<EMAIL>',
      availability_preferences: {
        preferred_days: ['Tuesday', 'Thursday'],
        preferred_times: ['afternoon'],
      },
    });

    // Seed class groups
    await createClassGroup({
      program_name: 'Computer Science',
      year_level: 1,
      group_size: 45,
    });
    
    await createClassGroup({
      program_name: 'Data Science',
      year_level: 2,
      group_size: 28,
    });

    // Seed modules
    await createModule({
      module_name: 'Introduction to Programming',
      required_hours: 4,
      practical_or_theory: 'Theory',
    });
    
    await createModule({
      module_name: 'Advanced Databases',
      required_hours: 3,
      practical_or_theory: 'Practical',
    });

    console.log('Initial data seeded successfully');
  } catch (error) {
    console.error('Error seeding initial data:', error);
    throw error;
  }
};
