export interface Classroom {
  id: string;
  room_number: string;
  capacity: number;
  equipment_details: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface Lecturer {
  id: string;
  name: string;
  contact_info: string;
  availability_preferences: {
    preferred_days: string[];
    preferred_times: string[];
  };
  // Keep the old format for backward compatibility during migration
  availability_preferences_json?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface ClassGroup {
  id: string;
  program_name: string;
  year_level: number;
  group_size: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface Module {
  id: string;
  module_name: string;
  required_hours: number;
  practical_or_theory: 'Practical' | 'Theory';
  created_at?: Date;
  updated_at?: Date;
}

export interface ScheduleEntry {
  id: string;
  module_id: string;
  lecturer_id: string;
  classroom_id: string;
  group_id: string;
  start_time: string; // e.g., "09:00"
  end_time: string; // e.g., "10:00"
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday';
  is_online: boolean;
  is_conflicted?: boolean; // Optional: for UI highlighting
  created_at?: Date;
  updated_at?: Date;
}

// Firestore-specific types for data operations
export interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

// Helper types for form data (without timestamps)
export type ClassroomFormData = Omit<Classroom, 'id' | 'created_at' | 'updated_at'>;
export type LecturerFormData = Omit<Lecturer, 'id' | 'created_at' | 'updated_at' | 'availability_preferences_json'>;
export type ClassGroupFormData = Omit<ClassGroup, 'id' | 'created_at' | 'updated_at'>;
export type ModuleFormData = Omit<Module, 'id' | 'created_at' | 'updated_at'>;
export type ScheduleEntryFormData = Omit<ScheduleEntry, 'id' | 'created_at' | 'updated_at' | 'is_conflicted'>;
