import type { ScheduleEntry, Classroom, Lecturer, ClassGroup, Module } from './types';

// Mock Data
let mockClassrooms: Classroom[] = [
  { id: 'c1', room_number: 'A101', capacity: 50, equipment_details: 'Projector, Whiteboard' },
  { id: 'c2', room_number: 'B203', capacity: 30, equipment_details: 'Computers, Projector' },
];

let mockLecturers: Lecturer[] = [
  { id: 'l1', name: 'Dr. <PERSON>', contact_info: '<EMAIL>', availability_preferences_json: '{"preferred_days": ["Monday", "Wednesday"], "preferred_times": ["morning"]}' },
  { id: 'l2', name: 'Prof<PERSON>', contact_info: '<EMAIL>', availability_preferences_json: '{"preferred_days": ["Tuesday", "Thursday"], "preferred_times": ["afternoon"]}' },
];

let mockClassGroups: ClassGroup[] = [
  { id: 'g1', program_name: 'Computer Science', year_level: 1, group_size: 45 },
  { id: 'g2', program_name: 'Data Science', year_level: 2, group_size: 28 },
];

let mockModules: Module[] = [
  { id: 'm1', module_name: 'Introduction to Programming', required_hours: 4, practical_or_theory: 'Theory' },
  { id: 'm2', module_name: 'Advanced Databases', required_hours: 3, practical_or_theory: 'Practical' },
];

let mockScheduleEntries: ScheduleEntry[] = [
  { id: 'se1', module_id: 'm1', lecturer_id: 'l1', classroom_id: 'c1', group_id: 'g1', start_time: '09:00', end_time: '10:00', day_of_week: 'Monday', is_online: false, is_conflicted: false },
  { id: 'se2', module_id: 'm2', lecturer_id: 'l2', classroom_id: 'c2', group_id: 'g2', start_time: '11:00', end_time: '12:00', day_of_week: 'Tuesday', is_online: false, is_conflicted: false },
  { id: 'se3', module_id: 'm1', lecturer_id: 'l1', classroom_id: 'c1', group_id: 'g2', start_time: '09:00', end_time: '10:00', day_of_week: 'Wednesday', is_online: false, is_conflicted: true }, // Example of a conflict
];

const simulateDelay = (ms: number) => new Promise(res => setTimeout(res, ms));

const checkConflicts = (entry: ScheduleEntry, entries: ScheduleEntry[]): boolean => {
  return entries.some(e => {
    if (e.id === entry.id) return false;
    if (e.day_of_week !== entry.day_of_week) return false;

    const entryStart = parseInt(entry.start_time.replace(':', ''), 10);
    const entryEnd = parseInt(entry.end_time.replace(':', ''), 10);
    const eStart = parseInt(e.start_time.replace(':', ''), 10);
    const eEnd = parseInt(e.end_time.replace(':', ''), 10);
    
    // Check for time overlap
    if (entryStart < eEnd && entryEnd > eStart) {
      // If time overlaps, check for resource conflict
      if (e.lecturer_id === entry.lecturer_id || e.classroom_id === entry.classroom_id || e.group_id === entry.group_id) {
        return true;
      }
    }
    return false;
  });
};

const updateConflicts = () => {
    mockScheduleEntries.forEach(entry => {
        entry.is_conflicted = checkConflicts(entry, mockScheduleEntries);
    });
};

// CRUD for Classrooms
export const getClassrooms = async (): Promise<Classroom[]> => { await simulateDelay(100); return mockClassrooms; };
export const createClassroom = async (data: Omit<Classroom, 'id'>): Promise<Classroom> => {
    await simulateDelay(100);
    const newClassroom = { ...data, id: `c${Date.now()}` };
    mockClassrooms.push(newClassroom);
    return newClassroom;
};
export const updateClassroom = async (id: string, data: Partial<Classroom>): Promise<Classroom> => {
    await simulateDelay(100);
    let updatedClassroom: Classroom | null = null;
    mockClassrooms = mockClassrooms.map(c => {
        if (c.id === id) {
            updatedClassroom = { ...c, ...data };
            return updatedClassroom;
        }
        return c;
    });
    if (updatedClassroom) return updatedClassroom;
    throw new Error("Classroom not found");
};
export const deleteClassroom = async (id: string): Promise<void> => {
    await simulateDelay(50);
    mockClassrooms = mockClassrooms.filter(c => c.id !== id);
};

// CRUD for Lecturers
export const getLecturers = async (): Promise<Lecturer[]> => { await simulateDelay(100); return mockLecturers; };
export const createLecturer = async (data: Omit<Lecturer, 'id'>): Promise<Lecturer> => {
    await simulateDelay(100);
    const newLecturer = { ...data, id: `l${Date.now()}` };
    mockLecturers.push(newLecturer);
    return newLecturer;
};
export const updateLecturer = async (id: string, data: Partial<Lecturer>): Promise<Lecturer> => {
    await simulateDelay(100);
    let updatedLecturer: Lecturer | null = null;
    mockLecturers = mockLecturers.map(l => {
        if (l.id === id) {
            updatedLecturer = { ...l, ...data };
            return updatedLecturer;
        }
        return l;
    });
    if (updatedLecturer) return updatedLecturer;
    throw new Error("Lecturer not found");
};
export const deleteLecturer = async (id: string): Promise<void> => {
    await simulateDelay(50);
    mockLecturers = mockLecturers.filter(l => l.id !== id);
};

// CRUD for Class Groups
export const getClassGroups = async (): Promise<ClassGroup[]> => { await simulateDelay(100); return mockClassGroups; };
export const createClassGroup = async (data: Omit<ClassGroup, 'id'>): Promise<ClassGroup> => {
    await simulateDelay(100);
    const newClassGroup = { ...data, id: `g${Date.now()}` };
    mockClassGroups.push(newClassGroup);
    return newClassGroup;
};
export const updateClassGroup = async (id: string, data: Partial<ClassGroup>): Promise<ClassGroup> => {
    await simulateDelay(100);
    let updatedClassGroup: ClassGroup | null = null;
    mockClassGroups = mockClassGroups.map(g => {
        if (g.id === id) {
            updatedClassGroup = { ...g, ...data };
            return updatedClassGroup;
        }
        return g;
    });
    if (updatedClassGroup) return updatedClassGroup;
    throw new Error("Class Group not found");
};
export const deleteClassGroup = async (id: string): Promise<void> => {
    await simulateDelay(50);
    mockClassGroups = mockClassGroups.filter(g => g.id !== id);
};

// CRUD for Modules
export const getModules = async (): Promise<Module[]> => { await simulateDelay(100); return mockModules; };
export const createModule = async (data: Omit<Module, 'id'>): Promise<Module> => {
    await simulateDelay(100);
    const newModule = { ...data, id: `m${Date.now()}` };
    mockModules.push(newModule);
    return newModule;
};
export const updateModule = async (id: string, data: Partial<Module>): Promise<Module> => {
    await simulateDelay(100);
    let updatedModule: Module | null = null;
    mockModules = mockModules.map(m => {
        if (m.id === id) {
            updatedModule = { ...m, ...data };
            return updatedModule;
        }
        return m;
    });
    if (updatedModule) return updatedModule;
    throw new Error("Module not found");
};
export const deleteModule = async (id: string): Promise<void> => {
    await simulateDelay(50);
    mockModules = mockModules.filter(m => m.id !== id);
};

// CRUD for Schedule Entries
export const getScheduleEntries = async (): Promise<ScheduleEntry[]> => {
    await simulateDelay(200);
    updateConflicts();
    return mockScheduleEntries;
};

export const createScheduleEntry = async (data: Omit<ScheduleEntry, 'id'>): Promise<ScheduleEntry> => {
    await simulateDelay(100);
    const newEntry = { ...data, id: `se${Date.now()}` };
    mockScheduleEntries.push(newEntry);
    updateConflicts();
    return newEntry;
};

export const updateScheduleEntry = async (id: string, data: Partial<ScheduleEntry>): Promise<ScheduleEntry> => {
    await simulateDelay(100);
    let updatedEntry: ScheduleEntry | null = null;
    mockScheduleEntries = mockScheduleEntries.map(e => {
        if (e.id === id) {
            updatedEntry = { ...e, ...data };
            return updatedEntry;
        }
        return e;
    });
    updateConflicts();
    if (updatedEntry) return updatedEntry;
    throw new Error("Entry not found");
};

export const deleteScheduleEntry = async (id: string): Promise<void> => {
    await simulateDelay(50);
    mockScheduleEntries = mockScheduleEntries.filter(e => e.id !== id);
    updateConflicts();
};
