import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth, connectAuthEmulator } from 'firebase/auth';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Auth
export const auth = getAuth(app);

// Connect to emulators in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Only run on client side and in development
  const isEmulatorConnected = (window as any).__FIREBASE_EMULATOR_CONNECTED__;
  
  if (!isEmulatorConnected) {
    try {
      // Connect to Firestore emulator
      connectFirestoreEmulator(db, 'localhost', 8080);
      
      // Connect to Auth emulator
      connectAuthEmulator(auth, 'http://localhost:9099');
      
      // Mark as connected to avoid reconnection
      (window as any).__FIREBASE_EMULATOR_CONNECTED__ = true;
      
      console.log('Connected to Firebase emulators');
    } catch (error) {
      console.log('Firebase emulators not available, using production');
    }
  }
}

export default app;
