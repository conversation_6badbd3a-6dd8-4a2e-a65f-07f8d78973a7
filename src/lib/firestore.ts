import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot,
  FirestoreError,
} from 'firebase/firestore';
import { db } from './firebase';
import type {
  Classroom,
  Lecturer,
  ClassGroup,
  Module,
  ScheduleEntry,
  ClassroomFormData,
  LecturerFormData,
  ClassGroupFormData,
  ModuleFormData,
  ScheduleEntryFormData,
} from './types';

// Helper function to convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp && timestamp.toDate) {
    return timestamp.toDate();
  }
  return new Date();
};

// Helper function to convert Firestore document to typed object
const convertDoc = <T>(doc: QueryDocumentSnapshot<DocumentData>): T => {
  const data = doc.data();
  return {
    id: doc.id,
    ...data,
    created_at: data.created_at ? convertTimestamp(data.created_at) : undefined,
    updated_at: data.updated_at ? convertTimestamp(data.updated_at) : undefined,
  } as T;
};

// Error handling wrapper
const handleFirestoreError = (error: any, operation: string): never => {
  console.error(`Firestore ${operation} error:`, error);
  
  if (error.code === 'permission-denied') {
    throw new Error('Permission denied. Please check your authentication.');
  } else if (error.code === 'not-found') {
    throw new Error('Document not found.');
  } else if (error.code === 'already-exists') {
    throw new Error('Document already exists.');
  } else if (error.code === 'unavailable') {
    throw new Error('Service temporarily unavailable. Please try again.');
  } else {
    throw new Error(`Failed to ${operation}. Please try again.`);
  }
};

// Generic CRUD operations
class FirestoreService<T, TFormData> {
  constructor(private collectionName: string) {}

  async getAll(): Promise<T[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        orderBy('created_at', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => convertDoc<T>(doc));
    } catch (error) {
      handleFirestoreError(error, 'fetch data');
    }
  }

  async getById(id: string): Promise<T | null> {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return convertDoc<T>(docSnap as QueryDocumentSnapshot<DocumentData>);
      }
      return null;
    } catch (error) {
      handleFirestoreError(error, 'fetch document');
    }
  }

  async create(data: TFormData): Promise<T> {
    try {
      const now = Timestamp.now();
      const docData = {
        ...data,
        created_at: now,
        updated_at: now,
      };
      
      const docRef = await addDoc(collection(db, this.collectionName), docData);
      const newDoc = await this.getById(docRef.id);
      
      if (!newDoc) {
        throw new Error('Failed to retrieve created document');
      }
      
      return newDoc;
    } catch (error) {
      handleFirestoreError(error, 'create document');
    }
  }

  async update(id: string, data: Partial<TFormData>): Promise<T> {
    try {
      const docRef = doc(db, this.collectionName, id);
      const updateData = {
        ...data,
        updated_at: Timestamp.now(),
      };
      
      await updateDoc(docRef, updateData);
      const updatedDoc = await this.getById(id);
      
      if (!updatedDoc) {
        throw new Error('Failed to retrieve updated document');
      }
      
      return updatedDoc;
    } catch (error) {
      handleFirestoreError(error, 'update document');
    }
  }

  async delete(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.collectionName, id);
      await deleteDoc(docRef);
    } catch (error) {
      handleFirestoreError(error, 'delete document');
    }
  }

  async query(field: string, operator: any, value: any): Promise<T[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where(field, operator, value),
        orderBy('created_at', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => convertDoc<T>(doc));
    } catch (error) {
      handleFirestoreError(error, 'query documents');
    }
  }
}

// Service instances for each collection
export const classroomService = new FirestoreService<Classroom, ClassroomFormData>('classrooms');
export const lecturerService = new FirestoreService<Lecturer, LecturerFormData>('lecturers');
export const classGroupService = new FirestoreService<ClassGroup, ClassGroupFormData>('class_groups');
export const moduleService = new FirestoreService<Module, ModuleFormData>('modules');
export const scheduleEntryService = new FirestoreService<ScheduleEntry, ScheduleEntryFormData>('schedule_entries');

// Specialized methods for specific collections

// Lecturer-specific methods
export const lecturerServiceExtended = {
  ...lecturerService,
  
  async getByAvailability(day: string, time: string): Promise<Lecturer[]> {
    try {
      const allLecturers = await lecturerService.getAll();
      return allLecturers.filter(lecturer => {
        const prefs = lecturer.availability_preferences;
        return prefs.preferred_days.includes(day) && prefs.preferred_times.includes(time);
      });
    } catch (error) {
      handleFirestoreError(error, 'query lecturers by availability');
    }
  },
};

// Schedule Entry-specific methods
export const scheduleEntryServiceExtended = {
  ...scheduleEntryService,
  
  async getByDay(day: string): Promise<ScheduleEntry[]> {
    return scheduleEntryService.query('day_of_week', '==', day);
  },
  
  async getByLecturer(lecturerId: string): Promise<ScheduleEntry[]> {
    return scheduleEntryService.query('lecturer_id', '==', lecturerId);
  },
  
  async getByClassroom(classroomId: string): Promise<ScheduleEntry[]> {
    return scheduleEntryService.query('classroom_id', '==', classroomId);
  },
  
  async getByGroup(groupId: string): Promise<ScheduleEntry[]> {
    return scheduleEntryService.query('group_id', '==', groupId);
  },
  
  async checkConflicts(entry: ScheduleEntryFormData): Promise<ScheduleEntry[]> {
    try {
      const dayEntries = await this.getByDay(entry.day_of_week);
      
      return dayEntries.filter(existing => {
        // Check time overlap
        const existingStart = existing.start_time;
        const existingEnd = existing.end_time;
        const newStart = entry.start_time;
        const newEnd = entry.end_time;
        
        const timeOverlap = (newStart < existingEnd && newEnd > existingStart);
        
        if (!timeOverlap) return false;
        
        // Check resource conflicts
        return (
          existing.lecturer_id === entry.lecturer_id ||
          (existing.classroom_id === entry.classroom_id && !entry.is_online) ||
          existing.group_id === entry.group_id
        );
      });
    } catch (error) {
      handleFirestoreError(error, 'check conflicts');
    }
  },
};
