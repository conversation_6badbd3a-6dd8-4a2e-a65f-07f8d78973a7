// Conflict resolution AI agent.
//
// - conflictResolutionSuggestions - A function that provides conflict resolution suggestions.
// - ConflictResolutionInput - The input type for the conflictResolutionSuggestions function.
// - ConflictResolutionOutput - The return type for the conflictResolutionSuggestions function.

'use server';

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ConflictResolutionInputSchema = z.object({
  scheduleEntries: z
    .array(
      z.object({
        id: z.string(),
        module_id: z.string(),
        lecturer_id: z.string(),
        classroom_id: z.string(),
        group_id: z.string(),
        start_time: z.string(),
        end_time: z.string(),
        is_online: z.boolean(),
      })
    )
    .describe('An array of schedule entries representing the current timetable.'),
  lecturers: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        contact_info: z.string(),
        availability_preferences_json: z.string(),
      })
    )
    .describe('An array of lecturer objects with their availability preferences.'),
  classrooms: z
    .array(
      z.object({
        id: z.string(),
        room_number: z.string(),
        capacity: z.number(),
        equipment_details: z.string(),
      })
    )
    .describe('An array of classroom objects with their capacity and equipment details.'),
  requirements: z
    .string()
    .describe('A string containing a list of requirements and preferences for the schedule.'),
});

export type ConflictResolutionInput = z.infer<typeof ConflictResolutionInputSchema>;

const ConflictResolutionOutputSchema = z.object({
  suggestions: z
    .array(z.string())
    .describe('An array of suggestions for resolving scheduling conflicts.'),
});

export type ConflictResolutionOutput = z.infer<typeof ConflictResolutionOutputSchema>;

export async function conflictResolutionSuggestions(input: ConflictResolutionInput): Promise<ConflictResolutionOutput> {
  return conflictResolutionFlow(input);
}

const prompt = ai.definePrompt({
  name: 'conflictResolutionPrompt',
  input: {schema: ConflictResolutionInputSchema},
  output: {schema: ConflictResolutionOutputSchema},
  prompt: `You are an AI assistant that provides suggestions for resolving scheduling conflicts.

You are provided with the current schedule entries, lecturer availability, classroom constraints, and a list of requirements and preferences for the schedule.

Based on this information, generate a list of suggestions for resolving any conflicts and optimizing the timetable.

Schedule Entries:
{{#each scheduleEntries}}
- Module: {{module_id}}, Lecturer: {{lecturer_id}}, Classroom: {{classroom_id}}, Group: {{group_id}}, Time: {{start_time}} - {{end_time}}, Online: {{is_online}}
{{/each}}

Lecturer Availability:
{{#each lecturers}}
- Name: {{name}}, Availability: {{availability_preferences_json}}
{{/each}}

Classroom Constraints:
{{#each classrooms}}
- Room Number: {{room_number}}, Capacity: {{capacity}}, Equipment: {{equipment_details}}
{{/each}}

Requirements and Preferences: {{requirements}}

Suggestions:
`, // Ensure that the output is an array of strings
});

const conflictResolutionFlow = ai.defineFlow(
  {
    name: 'conflictResolutionFlow',
    inputSchema: ConflictResolutionInputSchema,
    outputSchema: ConflictResolutionOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
