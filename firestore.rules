rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for validation
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() &&
             request.auth.token.get('role', '') == 'admin';
    }

    function isValidString(field, minLength, maxLength) {
      return field is string &&
             field.size() >= minLength &&
             field.size() <= maxLength;
    }

    function isValidEmail(email) {
      return email is string && email.matches('.*@.*\\..*');
    }

    function hasRequiredFields(requiredFields) {
      return requiredFields.toSet().difference(resource.data.keys().toSet()).size() == 0;
    }

    // Classrooms collection
    match /classrooms/{classroomId} {
      allow read: if true; // Anyone can read classroom info for scheduling
      allow create, update, delete: if isAdmin();

      allow create: if isAuthenticated() &&
                   isValidString(resource.data.room_number, 1, 20) &&
                   isValidString(resource.data.building, 1, 50) &&
                   resource.data.capacity is int &&
                   resource.data.capacity > 0 &&
                   resource.data.capacity <= 1000 &&
                   resource.data.equipment is list &&
                   resource.data.is_available is bool &&
                   hasRequiredFields(['room_number', 'building', 'capacity', 'equipment', 'is_available']);

      allow update: if isAuthenticated() &&
                   (resource.data.diff(request.resource.data).affectedKeys()
                    .hasOnly(['capacity', 'equipment', 'is_available', 'updated_at']));
    }

    // Lecturers collection
    match /lecturers/{lecturerId} {
      allow read: if true; // Anyone can read lecturer info for scheduling
      allow create, update, delete: if isAdmin();

      allow create: if isAuthenticated() &&
                   isValidString(resource.data.name, 2, 100) &&
                   isValidEmail(resource.data.email) &&
                   isValidString(resource.data.department, 2, 100) &&
                   resource.data.specializations is list &&
                   resource.data.is_available is bool &&
                   hasRequiredFields(['name', 'email', 'department', 'specializations', 'is_available']);

      allow update: if isAuthenticated() &&
                   (resource.data.diff(request.resource.data).affectedKeys()
                    .hasOnly(['department', 'specializations', 'is_available', 'updated_at']));
    }

    // Modules collection
    match /modules/{moduleId} {
      allow read: if true; // Anyone can read module info for scheduling
      allow create, update, delete: if isAdmin();

      allow create: if isAuthenticated() &&
                   isValidString(resource.data.module_code, 2, 20) &&
                   isValidString(resource.data.module_name, 2, 200) &&
                   resource.data.credits is int &&
                   resource.data.credits > 0 &&
                   resource.data.credits <= 50 &&
                   isValidString(resource.data.department, 2, 100) &&
                   resource.data.prerequisites is list &&
                   hasRequiredFields(['module_code', 'module_name', 'credits', 'department', 'prerequisites']);

      allow update: if isAuthenticated() &&
                   (resource.data.diff(request.resource.data).affectedKeys()
                    .hasOnly(['module_name', 'credits', 'prerequisites', 'updated_at']));
    }

    // Class Groups collection
    match /class_groups/{groupId} {
      allow read: if true; // Anyone can read group info for scheduling
      allow create, update, delete: if isAdmin();

      allow create: if isAuthenticated() &&
                   isValidString(resource.data.group_name, 1, 50) &&
                   resource.data.student_count is int &&
                   resource.data.student_count > 0 &&
                   resource.data.student_count <= 500 &&
                   isValidString(resource.data.academic_year, 4, 20) &&
                   isValidString(resource.data.program, 2, 100) &&
                   hasRequiredFields(['group_name', 'student_count', 'academic_year', 'program']);

      allow update: if isAuthenticated() &&
                   (resource.data.diff(request.resource.data).affectedKeys()
                    .hasOnly(['student_count', 'academic_year', 'program', 'updated_at']));
    }

    // Schedule Entries collection
    match /schedule_entries/{entryId} {
      allow read: if true; // Anyone can read schedule for viewing timetables
      allow create, update, delete: if isAuthenticated(); // Authenticated users can manage schedules

      allow create: if isAuthenticated() &&
                   isValidString(resource.data.module_id, 1, 50) &&
                   isValidString(resource.data.lecturer_id, 1, 50) &&
                   isValidString(resource.data.classroom_id, 1, 50) &&
                   isValidString(resource.data.group_id, 1, 50) &&
                   resource.data.day_of_week is int &&
                   resource.data.day_of_week >= 0 &&
                   resource.data.day_of_week <= 6 &&
                   resource.data.start_time is int &&
                   resource.data.start_time >= 0 &&
                   resource.data.start_time <= 23 &&
                   resource.data.end_time is int &&
                   resource.data.end_time > resource.data.start_time &&
                   resource.data.end_time <= 24 &&
                   resource.data.is_online is bool &&
                   resource.data.is_conflicted is bool &&
                   hasRequiredFields(['module_id', 'lecturer_id', 'classroom_id', 'group_id',
                                    'day_of_week', 'start_time', 'end_time', 'is_online', 'is_conflicted']);

      allow update: if isAuthenticated() &&
                   (resource.data.diff(request.resource.data).affectedKeys()
                    .hasOnly(['module_id', 'lecturer_id', 'classroom_id', 'group_id',
                             'day_of_week', 'start_time', 'end_time', 'is_online', 'is_conflicted', 'updated_at']));

      allow delete: if isAuthenticated();
    }

    // User profiles collection (for future authentication)
    match /users/{userId} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
      allow read: if isAdmin();

      allow create: if isAuthenticated() &&
                   request.auth.uid == userId &&
                   isValidString(resource.data.name, 2, 100) &&
                   isValidEmail(resource.data.email) &&
                   resource.data.role in ['admin', 'lecturer', 'student', 'staff'] &&
                   hasRequiredFields(['name', 'email', 'role']);
    }

    // System settings (admin only)
    match /settings/{settingId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Audit logs (read-only for admins, write for system)
    match /audit_logs/{logId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated(); // System can create audit logs
    }
  }
}