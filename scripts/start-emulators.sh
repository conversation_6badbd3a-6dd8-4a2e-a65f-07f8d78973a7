#!/bin/bash

# Start Firebase Emulators for CampusConcord Development
echo "🔥 Starting Firebase Emulators for CampusConcord..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "   npm install -g firebase-tools"
    exit 1
fi

# Check if logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login first:"
    echo "   firebase login"
    exit 1
fi

echo "✅ Firebase CLI is ready"
echo "🚀 Starting emulators..."
echo ""
echo "Emulator UI will be available at: http://localhost:4000"
echo "Firestore Emulator: localhost:8080"
echo "Auth Emulator: localhost:9099"
echo ""
echo "Press Ctrl+C to stop the emulators"
echo ""

# Start the emulators
firebase emulators:start
