#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up CampusFlow Scheduler for development...\n');

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local');
const envExamplePath = path.join(process.cwd(), '.env.example');

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    console.log('📋 Creating .env.local from .env.example...');
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env.local created successfully!');
    console.log('⚠️  Please update .env.local with your Firebase configuration.\n');
  } else {
    console.log('❌ .env.example not found. Please create it first.\n');
  }
} else {
  console.log('✅ .env.local already exists.\n');
}

// Instructions
console.log('📖 Next steps:');
console.log('1. Create a Firebase project at https://console.firebase.google.com/');
console.log('2. Enable Firestore Database');
console.log('3. Update .env.local with your Firebase configuration');
console.log('4. Run: npm run dev');
console.log('5. Use the "Seed Data" button to populate initial data\n');

console.log('🔧 Development commands:');
console.log('- npm run dev          # Start development server');
console.log('- npm run build        # Build for production');
console.log('- npm run lint         # Run linter');
console.log('- npm run typecheck    # Check TypeScript types\n');

console.log('📚 Documentation:');
console.log('- docs/implementation-plan.md  # Agent responsibilities');
console.log('- docs/firestore-setup.md      # Database setup guide');
console.log('- docs/blueprint.md            # Project overview\n');

console.log('🎉 Setup complete! Happy coding!');
