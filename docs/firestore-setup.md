# Firestore Database Setup & Configuration

## Overview
CampusFlow Scheduler uses Firestore as its NoSQL database. This document outlines the setup, configuration, and best practices for working with Firestore in this project.

## Firebase Project Configuration

### Environment Variables
Create a `.env.local` file in the project root with the following variables:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### Firebase Configuration File
Location: `src/lib/firebase.ts`

```typescript
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
```

## Collection Structure & Indexes

### Required Composite Indexes

1. **schedule_entries**
   - `day_of_week` (Ascending) + `start_time` (Ascending)
   - `lecturer_id` (Ascending) + `day_of_week` (Ascending) + `start_time` (Ascending)
   - `classroom_id` (Ascending) + `day_of_week` (Ascending) + `start_time` (Ascending)
   - `group_id` (Ascending) + `day_of_week` (Ascending) + `start_time` (Ascending)

2. **lecturers**
   - `name` (Ascending) + `created_at` (Descending)

3. **modules**
   - `module_name` (Ascending) + `practical_or_theory` (Ascending)

### Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all authenticated users for now
    // TODO: Implement proper role-based access control
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // Public read access for development (remove in production)
    match /{document=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

## Data Validation Schema

### Classroom Validation
```typescript
const classroomSchema = {
  room_number: { required: true, type: 'string', minLength: 1 },
  capacity: { required: true, type: 'number', min: 1 },
  equipment_details: { required: true, type: 'string' },
};
```

### Lecturer Validation
```typescript
const lecturerSchema = {
  name: { required: true, type: 'string', minLength: 2 },
  contact_info: { required: true, type: 'string', format: 'email' },
  availability_preferences: {
    preferred_days: { type: 'array', items: { enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'] } },
    preferred_times: { type: 'array', items: { enum: ['morning', 'afternoon', 'evening'] } },
  },
};
```

### Module Validation
```typescript
const moduleSchema = {
  module_name: { required: true, type: 'string', minLength: 2 },
  required_hours: { required: true, type: 'number', min: 1, max: 10 },
  practical_or_theory: { required: true, enum: ['Practical', 'Theory'] },
};
```

### Class Group Validation
```typescript
const classGroupSchema = {
  program_name: { required: true, type: 'string', minLength: 2 },
  year_level: { required: true, type: 'number', min: 1, max: 5 },
  group_size: { required: true, type: 'number', min: 1, max: 200 },
};
```

### Schedule Entry Validation
```typescript
const scheduleEntrySchema = {
  module_id: { required: true, type: 'string' },
  lecturer_id: { required: true, type: 'string' },
  classroom_id: { required: true, type: 'string' },
  group_id: { required: true, type: 'string' },
  start_time: { required: true, type: 'string', pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$' },
  end_time: { required: true, type: 'string', pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$' },
  day_of_week: { required: true, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'] },
  is_online: { required: true, type: 'boolean' },
};
```

## Best Practices

### 1. Error Handling
- Always wrap Firestore operations in try-catch blocks
- Provide meaningful error messages to users
- Log errors for debugging purposes
- Implement retry logic for transient failures

### 2. Performance Optimization
- Use pagination for large datasets
- Implement proper indexing for queries
- Cache frequently accessed data
- Use Firestore offline persistence

### 3. Data Consistency
- Use batch operations for related writes
- Implement proper validation before writes
- Use transactions for operations that must be atomic
- Handle concurrent modifications gracefully

### 4. Security
- Implement proper authentication
- Use Firestore security rules
- Validate data on both client and server side
- Sanitize user inputs

## Migration from Mock API

### Steps to Replace Mock Data:
1. Set up Firebase project and Firestore database
2. Create Firebase configuration file
3. Implement Firestore service functions
4. Replace mock API calls with Firestore operations
5. Update type definitions if needed
6. Test all CRUD operations
7. Implement proper error handling
8. Add loading states and user feedback

### Data Migration Script:
```typescript
// src/scripts/migrate-mock-data.ts
import { db } from '@/lib/firebase';
import { collection, addDoc, Timestamp } from 'firebase/firestore';

const migrateMockData = async () => {
  // Migration logic to move mock data to Firestore
  // This will be implemented by Agent 1
};
```

## Development Workflow

1. **Local Development**: Use Firestore emulator for development
2. **Testing**: Use separate Firestore project for testing
3. **Staging**: Use staging Firestore project
4. **Production**: Use production Firestore project with proper security rules

## Monitoring & Analytics

- Set up Firestore usage monitoring
- Implement performance tracking
- Monitor read/write operations
- Track query performance
- Set up alerts for unusual activity
