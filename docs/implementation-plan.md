# CampusFlow Scheduler - Implementation Plan

## Project Overview
CampusFlow Scheduler is a web-based academic scheduling application built with Next.js, TypeScript, and Firestore as the NoSQL database. The project uses Firebase Studio for development and deployment.

## Technology Stack
- **Frontend**: Next.js 14, React, TypeScript
- **UI Components**: Radix UI, Tailwind CSS, Shadcn/ui
- **Database**: Firestore (NoSQL)
- **AI Integration**: Google Genkit with Gemini 2.0 Flash
- **Authentication**: Firebase Auth (to be implemented)
- **Deployment**: Firebase Hosting

## Development Phases & Agent Responsibilities

### **Agent 1: Resource Management & Database Integration**
**Branch**: `feature/agent1-resource-management`

#### Primary Responsibilities:
1. **Firestore Integration**
   - Set up Firestore database configuration
   - Create Firebase service initialization
   - Implement Firestore CRUD operations for all collections

2. **Resource Management Pages**
   - Complete Lecturers management interface
   - Complete Modules management interface  
   - Complete Class Groups management interface
   - Enhance Classrooms management (already partially implemented)

3. **API Layer Enhancement**
   - Replace mock API with Firestore operations
   - Implement proper error handling
   - Add data validation and sanitization
   - Create reusable Firestore service functions

4. **UI Components**
   - Create reusable form components for resource management
   - Implement data tables with sorting, filtering, and pagination
   - Add confirmation dialogs for delete operations
   - Create loading states and error handling UI

#### Deliverables:
- [ ] Firestore configuration and initialization
- [ ] Complete CRUD operations for all resource collections
- [ ] Lecturers management page with full functionality
- [ ] Modules management page with full functionality
- [ ] Class Groups management page with full functionality
- [ ] Enhanced Classrooms management page
- [ ] Reusable UI components for resource management
- [ ] Comprehensive error handling and validation

### **Agent 2: Timetable & Scheduling Features** (Future)
**Branch**: `feature/agent2-timetable-scheduling`

#### Primary Responsibilities:
1. **Timetable Display Enhancement**
2. **Interactive Scheduling**
3. **Drag-and-Drop Functionality**
4. **Conflict Detection Logic**

### **Agent 3: AI Integration & Conflict Resolution** (Future)
**Branch**: `feature/agent3-ai-conflict-resolution`

#### Primary Responsibilities:
1. **AI Conflict Resolution Enhancement**
2. **Advanced Analytics**
3. **Optimization Algorithms**

## Database Collections (Firestore)

### Collection: `classrooms`
```typescript
interface Classroom {
  id: string;
  room_number: string;
  capacity: number;
  equipment_details: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}
```

### Collection: `lecturers`
```typescript
interface Lecturer {
  id: string;
  name: string;
  contact_info: string;
  availability_preferences: {
    preferred_days: string[];
    preferred_times: string[];
  };
  created_at: Timestamp;
  updated_at: Timestamp;
}
```

### Collection: `class_groups`
```typescript
interface ClassGroup {
  id: string;
  program_name: string;
  year_level: number;
  group_size: number;
  created_at: Timestamp;
  updated_at: Timestamp;
}
```

### Collection: `modules`
```typescript
interface Module {
  id: string;
  module_name: string;
  required_hours: number;
  practical_or_theory: 'Practical' | 'Theory';
  created_at: Timestamp;
  updated_at: Timestamp;
}
```

### Collection: `schedule_entries`
```typescript
interface ScheduleEntry {
  id: string;
  module_id: string;
  lecturer_id: string;
  classroom_id: string;
  group_id: string;
  start_time: string;
  end_time: string;
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday';
  is_online: boolean;
  is_conflicted?: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}
```

## Development Guidelines

### Code Standards
- Use TypeScript for all new code
- Follow React best practices and hooks patterns
- Implement proper error boundaries
- Use consistent naming conventions
- Add JSDoc comments for complex functions

### Database Best Practices
- Use Firestore security rules
- Implement proper indexing for queries
- Use batch operations for multiple writes
- Handle offline scenarios gracefully
- Implement proper data validation

### UI/UX Standards
- Follow the established design system
- Ensure responsive design across all devices
- Implement proper loading states
- Provide clear error messages
- Use consistent iconography

## Testing Strategy
- Unit tests for utility functions
- Integration tests for Firestore operations
- Component tests for UI interactions
- End-to-end tests for critical user flows

## Deployment Process
1. Development in feature branches
2. Code review and testing
3. Merge to main branch
4. Automated deployment to Firebase Hosting
5. Post-deployment verification

## Current Status
- ✅ Basic project structure and UI components
- ✅ Mock API implementation
- ✅ Basic timetable display
- ✅ AI conflict resolution foundation
- 🔄 **Agent 1 in progress**: Resource management and Firestore integration
- ⏳ Agent 2: Advanced timetable features
- ⏳ Agent 3: Enhanced AI features
