# Firestore Security Rules Documentation

## Overview

The Firestore security rules for CampusConcord implement a comprehensive access control system that balances security with functionality. The rules are designed to support the campus scheduling system while protecting sensitive data.

## Security Model

### Authentication Levels

1. **Public Access**: Unauthenticated users (read-only for public information)
2. **Authenticated Users**: Logged-in users (can manage schedules)
3. **Administrators**: Users with admin role (full access to resources)

### Role-Based Access Control

The system uses a role-based approach with the following roles:
- `admin`: Full system access
- `lecturer`: Can manage their own schedules and view resources
- `staff`: Can manage schedules and view resources
- `student`: Can view schedules and resources

## Collection-Specific Rules

### Classrooms Collection (`/classrooms/{classroomId}`)

**Read Access**: Public (anyone can view classroom information for scheduling)
**Write Access**: Administrators only

**Validation Rules**:
- `room_number`: String, 1-20 characters
- `building`: String, 1-50 characters  
- `capacity`: Integer, 1-1000
- `equipment`: Array of equipment items
- `is_available`: Boolean

**Update Restrictions**: Only capacity, equipment, availability, and timestamp can be updated

### Lecturers Collection (`/lecturers/{lecturerId}`)

**Read Access**: Public (for scheduling purposes)
**Write Access**: Administrators only

**Validation Rules**:
- `name`: String, 2-100 characters
- `email`: Valid email format
- `department`: String, 2-100 characters
- `specializations`: Array of specialization areas
- `is_available`: Boolean

**Update Restrictions**: Only department, specializations, availability, and timestamp can be updated

### Modules Collection (`/modules/{moduleId}`)

**Read Access**: Public (for course information)
**Write Access**: Administrators only

**Validation Rules**:
- `module_code`: String, 2-20 characters (e.g., "CS101")
- `module_name`: String, 2-200 characters
- `credits`: Integer, 1-50
- `department`: String, 2-100 characters
- `prerequisites`: Array of prerequisite module codes

**Update Restrictions**: Only name, credits, prerequisites, and timestamp can be updated

### Class Groups Collection (`/class_groups/{groupId}`)

**Read Access**: Public (for scheduling)
**Write Access**: Administrators only

**Validation Rules**:
- `group_name`: String, 1-50 characters
- `student_count`: Integer, 1-500
- `academic_year`: String, 4-20 characters (e.g., "2024/2025")
- `program`: String, 2-100 characters

**Update Restrictions**: Only student count, academic year, program, and timestamp can be updated

### Schedule Entries Collection (`/schedule_entries/{entryId}`)

**Read Access**: Public (anyone can view timetables)
**Write Access**: Authenticated users

**Validation Rules**:
- `module_id`: String reference to module
- `lecturer_id`: String reference to lecturer
- `classroom_id`: String reference to classroom
- `group_id`: String reference to class group
- `day_of_week`: Integer, 0-6 (Sunday=0, Monday=1, etc.)
- `start_time`: Integer, 0-23 (24-hour format)
- `end_time`: Integer, must be greater than start_time, max 24
- `is_online`: Boolean
- `is_conflicted`: Boolean

**Business Logic**: End time must be after start time, valid day/time ranges

### User Profiles Collection (`/users/{userId}`)

**Read Access**: Own profile or administrators
**Write Access**: Own profile only

**Validation Rules**:
- `name`: String, 2-100 characters
- `email`: Valid email format
- `role`: Must be one of: 'admin', 'lecturer', 'student', 'staff'

**Security**: Users can only access their own profile data

### System Collections

#### Settings (`/settings/{settingId}`)
- **Read**: Authenticated users
- **Write**: Administrators only

#### Audit Logs (`/audit_logs/{logId}`)
- **Read**: Administrators only
- **Write**: System/authenticated users (for logging)

## Helper Functions

### `isAuthenticated()`
Checks if the user is logged in with a valid authentication token.

### `isAdmin()`
Verifies if the authenticated user has admin role privileges.

### `isValidString(field, minLength, maxLength)`
Validates string fields with length constraints.

### `isValidEmail(email)`
Validates email format using regex pattern matching.

### `hasRequiredFields(requiredFields)`
Ensures all required fields are present in the document.

## Security Features

### Data Validation
- All input data is validated for type, format, and constraints
- Required fields are enforced
- Business logic constraints are applied (e.g., end_time > start_time)

### Access Control
- Role-based permissions prevent unauthorized access
- Users can only modify their own data (except admins)
- Public read access for scheduling information

### Update Restrictions
- Specific fields can be updated to prevent unauthorized changes
- Critical fields like IDs and creation timestamps are protected
- Audit trail support through update timestamps

## Development vs Production

### Current State (Development)
- Some collections allow public read access for ease of development
- Authentication is required for write operations

### Production Recommendations
1. Implement proper user authentication
2. Add more granular role-based permissions
3. Consider adding rate limiting
4. Implement audit logging for all operations
5. Add data encryption for sensitive fields

## Testing the Rules

### Using Firebase Emulator
```bash
# Start emulators with security rules
firebase emulators:start

# Test rules in emulator UI
# Visit http://localhost:4000 and use the Rules Playground
```

### Common Test Scenarios
1. Unauthenticated user trying to read classroom data (should succeed)
2. Unauthenticated user trying to create schedule entry (should fail)
3. Authenticated user creating valid schedule entry (should succeed)
4. User trying to update another user's profile (should fail)
5. Admin user managing any resource (should succeed)

## Error Handling

Common error codes you might encounter:
- `PERMISSION_DENIED`: User lacks required permissions
- `INVALID_ARGUMENT`: Data validation failed
- `FAILED_PRECONDITION`: Business logic constraints violated

## Maintenance

### Regular Reviews
- Review access patterns and adjust permissions
- Monitor for security violations in audit logs
- Update validation rules as data requirements change

### Performance Considerations
- Complex rules can impact query performance
- Consider caching frequently accessed validation data
- Monitor rule evaluation costs in Firebase console
