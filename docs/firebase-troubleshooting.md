# Firebase Emulator Troubleshooting Guide

## The Error You're Seeing

```
Error: [2025-06-30T09:41:29.292Z] @firebase/firestore: "Firestore (11.9.0): Could not reach Cloud Firestore backend. Connection failed 1 times. Most recent error: FirebaseError: [code=unavailable]: The operation could not be completed
```

This error means your app is trying to connect to the production Firestore backend instead of the local emulator.

## Quick Fix Steps

### 1. Start Firebase Emulators First

Before running your Next.js app, you need to start the Firebase emulators:

```bash
# Option 1: Use the npm script
npm run emulators

# Option 2: Use Firebase CLI directly
firebase emulators:start

# Option 3: Use the shell script
./scripts/start-emulators.sh
```

### 2. Verify Emulators Are Running

You should see output like:
```
✔  firestore: Firestore Emulator UI websocket is running on 8080.
✔  auth: Auth Emulator UI is running on http://localhost:4000/auth
✔  ui: Emulator UI is running on http://localhost:4000
```

### 3. Start Your Next.js App

In a **separate terminal**, start your app:
```bash
npm run dev
```

### 4. Check Browser Console

You should see these messages in your browser console:
```
✅ Connected to Firestore emulator on localhost:8080
✅ Connected to Auth emulator on localhost:9099
🔥 Firebase emulators connected successfully
```

## Common Issues & Solutions

### Issue 1: "Firebase emulators not available"

**Symptoms:** Console shows "❌ Failed to connect to Firebase emulators"

**Solutions:**
1. Make sure emulators are running first
2. Check if ports 8080 and 9099 are available
3. Try restarting the emulators

### Issue 2: Port Already in Use

**Symptoms:** Error starting emulators about ports being in use

**Solutions:**
```bash
# Kill processes on Firebase emulator ports
lsof -ti:8080 | xargs kill -9
lsof -ti:9099 | xargs kill -9
lsof -ti:4000 | xargs kill -9

# Then restart emulators
npm run emulators
```

### Issue 3: Firebase CLI Not Installed

**Symptoms:** "firebase: command not found"

**Solution:**
```bash
npm install -g firebase-tools
firebase login
```

### Issue 4: Not Logged Into Firebase

**Symptoms:** Authentication errors when starting emulators

**Solution:**
```bash
firebase login
firebase use --add  # Select your project
```

## Development Workflow

### Recommended Startup Order:

1. **Terminal 1:** Start Firebase emulators
   ```bash
   npm run emulators
   ```

2. **Terminal 2:** Start Next.js development server
   ```bash
   npm run dev
   ```

3. **Browser:** Open http://localhost:9002

4. **Emulator UI:** Open http://localhost:4000 (optional, for debugging)

### Stopping Everything:

1. Press `Ctrl+C` in both terminals
2. Verify ports are free: `lsof -i :8080,9099,4000,9002`

## Verification Checklist

- [ ] Firebase CLI installed (`firebase --version`)
- [ ] Logged into Firebase (`firebase projects:list`)
- [ ] Emulators running (check http://localhost:4000)
- [ ] Next.js app running (check http://localhost:9002)
- [ ] Browser console shows emulator connection success
- [ ] No "Could not reach Cloud Firestore backend" errors

## Environment Variables

Make sure your `.env.local` has the correct Firebase config:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
```

## Still Having Issues?

1. Clear browser cache and reload
2. Restart both emulators and Next.js app
3. Check if any antivirus/firewall is blocking local ports
4. Try using `127.0.0.1` instead of `localhost` in firebase.ts
