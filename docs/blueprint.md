# **App Name**: CampusFlow Scheduler

## Core Features:

- Timetable Display: Dashboard with a calendar-style view to display the weekly timetable, rendering schedule entries fetched from the backend API.
- Conflict Highlighting: Visual highlighting of conflicted schedule entries with a distinct visual cue (e.g., red border).
- Interactive Scheduling: Allow users to create a new schedule entry by selecting time slots in the Timetable Display and picking elements of modules, lecturers, classrooms, and class groups from a displayed form.
- Drag-and-Drop Rescheduling: Drag-and-drop support for rescheduling entries in the Timetable Display.
- Resource Management: Pages/views for Classrooms, Lecturers, Class Groups and Modules. List display and forms or modals for creation and editing of resources are presented in these pages.
- Conflict Resolution Tool: Implement suggestions for resolving timetable conflicts, based on analyzing a schedule's compliance with an array of requirements and preferences (like instructor availabilities). Generative AI acts as a tool providing useful solutions and optimizations, which will or will not be incorporated in the output depending on the constraints and weights that are being factored into the analysis.

## Database Architecture (Firestore NoSQL)

### Collections Structure:

#### 1. **classrooms** Collection
```typescript
{
  id: string,
  room_number: string,
  capacity: number,
  equipment_details: string,
  created_at: Timestamp,
  updated_at: Timestamp
}
```

#### 2. **lecturers** Collection
```typescript
{
  id: string,
  name: string,
  contact_info: string,
  availability_preferences: {
    preferred_days: string[],
    preferred_times: string[]
  },
  created_at: Timestamp,
  updated_at: Timestamp
}
```

#### 3. **class_groups** Collection
```typescript
{
  id: string,
  program_name: string,
  year_level: number,
  group_size: number,
  created_at: Timestamp,
  updated_at: Timestamp
}
```

#### 4. **modules** Collection
```typescript
{
  id: string,
  module_name: string,
  required_hours: number,
  practical_or_theory: 'Practical' | 'Theory',
  created_at: Timestamp,
  updated_at: Timestamp
}
```

#### 5. **schedule_entries** Collection
```typescript
{
  id: string,
  module_id: string,
  lecturer_id: string,
  classroom_id: string,
  group_id: string,
  start_time: string, // e.g., "09:00"
  end_time: string,   // e.g., "10:00"
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday',
  is_online: boolean,
  is_conflicted?: boolean,
  created_at: Timestamp,
  updated_at: Timestamp
}
```

## Style Guidelines:

- Primary color: Navy blue (#3B527A) to evoke trust, knowledge, and a professional academic environment.
- Background color: Light gray (#F0F4F8), offering a clean, neutral backdrop that ensures readability and focus.
- Accent color: Teal (#468999) to highlight interactive elements and key actions.
- Font: 'Inter', a sans-serif font, for both headlines and body text. It gives the app a modern, clean and highly readable appearance.
- Code font: 'Source Code Pro' for displaying any code snippets or technical information.
- Consistent use of icons from a modern set (e.g. Font Awesome or Material Design) to represent actions and resource types.
- Clean, grid-based layout for the dashboard to facilitate the easy overview of the schedule.